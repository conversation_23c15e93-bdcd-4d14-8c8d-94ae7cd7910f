<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Communication/etc/communication.xsd">

    <topic name="site.techoutlet.cancelorder" request="MadHat\SiteIntegrationCancelorder\Api\Data\CancelorderDataInterface[]">
        <handler name="madhat.siteintegrationcancelorder.consumer" type="MadHat\SiteIntegrationCancelorder\Model\RabbitMQ\CancelorderConsumer" method="processMessage"/>
    </topic>

    <!-- For Developer Only -->
<!--    <topic name="site.techoutlet-dev.cancelorder.olegh" request="MadHat\SiteIntegrationCancelorder\Api\Data\CancelorderDataInterface[]">-->
<!--        <handler name="madhat.siteintegrationcancelorder.consumer" type="MadHat\SiteIntegrationCancelorder\Model\RabbitMQ\CancelorderConsumer" method="processMessage"/>-->
<!--    </topic>-->
</config>
