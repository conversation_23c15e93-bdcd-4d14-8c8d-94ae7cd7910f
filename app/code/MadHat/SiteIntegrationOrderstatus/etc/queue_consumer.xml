<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="madhat.siteintegrationorderstatus.consumer" queue="site.techoutlet.orderstatus" connection="amqpSITE"
              handler="MadHat\SiteIntegrationOrderstatus\Model\RabbitMQ\OrderstatusConsumer::processMessage"/>

    <!-- For Developer Only -->
<!--    <consumer name="madhat.siteintegrationorderstatus.consumer" queue="site.techoutlet-dev.orderstatus.olegh" connection="amqpSITE"-->
<!--              handler="MadHat\SiteIntegrationOrderstatus\Model\RabbitMQ\OrderstatusConsumer::processMessage"/>-->
</config>
