<?php

namespace MadHat\SiteIntegrationOrderstatus\Model\RabbitMQ;

use MadHat\SiteIntegrationOrderstatus\Api\Data\OrderstatusDataInterface;
use MadHat\SiteIntegrationOrderstatus\Model\OrderstatusProcessor;
use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;
use Psr\Log\LoggerInterface;
use MadHat\DbLogger\Logger\DbLoggerSaver;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;

class OrderstatusConsumer
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var OrderstatusProcessor
     */
    private OrderstatusProcessor $orderstatusProcessor;

    /**
     * @var DbLoggerSaver
     */
    private DbLoggerSaver $dbLoggerSaver;

    /**
     * @var Emulation
     */
    private Emulation $emulation;

    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * @var SiteCoreHelper
     */
    private SiteCoreHelper $siteCoreHelper;

    /**
     * OrderstatusConsumer constructor.
     *
     * @param OrderstatusProcessor $orderstatusProcessor
     * @param LoggerInterface $logger
     * @param DbLoggerSaver $dbLoggerSaver
     * @param Emulation $emulation
     * @param StoreManagerInterface $storeManager
     * @param SiteCoreHelper $siteCoreHelper
     */
    public function __construct(
        OrderstatusProcessor $orderstatusProcessor,
        LoggerInterface $logger,
        DbLoggerSaver $dbLoggerSaver,
        Emulation $emulation,
        StoreManagerInterface $storeManager,
        SiteCoreHelper $siteCoreHelper
    ) {
        $this->orderstatusProcessor = $orderstatusProcessor;
        $this->logger = $logger;
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->emulation = $emulation;
        $this->storeManager = $storeManager;
        $this->siteCoreHelper = $siteCoreHelper;
    }

    /**
     * Consumer process start
     * @param OrderstatusDataInterface[] $message
     * @return void
     */
    public function processMessage(array $message): void
    {
        // Check if RabbitMQ mode is enabled
        if (!$this->siteCoreHelper->isRabbitMqMode()) {
            $this->logger->info(__('%1 => %2 Skipping message processing - Integration mode is not set to RabbitMQ',
                __CLASS__,
                __FUNCTION__
            ));
            return;
        }

        $this->logger->info(__('%1 => %2 Received Message: %3',
            __CLASS__,
            __FUNCTION__,
            print_r($message, true)
        ));

        $totalMessages = count($message);
        $successfulMessages = 0;
        $failedMessages = 0;
        $errors = [];

        try {
            foreach ($message as $item) {
                try {
                    $this->orderstatusProcessor->processItem($item);
                    $successfulMessages++;
                } catch (\Exception $e) {
                    $failedMessages++;
                    $errors[] = [
                        'Item' => $item,
                        'ErrorMessage' => $e->getMessage()
                    ];
                    $this->logger->error('Error processing item: ' . $e->getMessage());
                }
            }

            // Log the processing results
            $this->addDbLoggerOrderStatusRecord($totalMessages, $successfulMessages, $failedMessages, $errors);

        } catch (\InvalidArgumentException $exception) {
            $this->logger->error(__('%1 => %2 ERROR: %3',
                __CLASS__,
                __FUNCTION__,
                $exception->getMessage()
            ));
        }
    }

    /**
     * Function to log the processing results
     *
     * @param int $totalMessages
     * @param int $successfulMessages
     * @param int $failedMessages
     * @param array $errors
     * @return void
     */
    private function addDbLoggerOrderStatusRecord(int $totalMessages, int $successfulMessages, int $failedMessages, array $errors): void
    {
        $message = "Total Messages: " . $totalMessages;
        $message .= " | Successful Messages: " . $successfulMessages;
        $message .= " | Failed Messages: " . $failedMessages;

        if (!empty($errors)) {
            $message .= " | Errors: " . json_encode($errors);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            'Order Status Import Report',
            $message,
            'NOTICE',
            LogIdentifierProvider::ORDER_IMPORT
        );
        $this->emulation->stopEnvironmentEmulation();
    }
}
