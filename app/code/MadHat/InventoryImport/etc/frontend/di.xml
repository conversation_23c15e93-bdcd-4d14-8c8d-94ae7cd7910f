<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Model\Cart">
        <plugin name="check_min_qty_before_add_to_cart" type="MadHat\InventoryImport\Plugin\CheckMinQtyBeforeAddToCart"/>
    </type>
    <type name="Magento\Checkout\Model\Sidebar">
        <plugin name="check_min_qty_before_update_quote_item" type="MadHat\InventoryImport\Plugin\CheckMinQtyBeforeUpdateQuoteItem"/>
    </type>
    <type name="Magento\ConfigurableProduct\Model\Product\Type\Configurable">
        <plugin name="is_option_salable" type="MadHat\InventoryImport\Plugin\Model\Product\Type\Configurable\IsSalableOptionPlugin"/>
    </type>

    <type name="Magento\Eav\Model\Entity\Attribute\Frontend\DefaultFrontend">
        <plugin name="filter_options_for_inventory_status" type="MadHat\InventoryImport\Plugin\Model\Entity\Attribute\Frontend\DefaultFrontendPlugin" />
    </type>
</config>
