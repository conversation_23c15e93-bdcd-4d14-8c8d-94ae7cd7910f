<?php
declare(strict_types=1);

namespace MadHat\InventoryImport\Plugin\Model\Entity\Attribute\Frontend;

use Magento\Eav\Model\Entity\Attribute\Frontend\DefaultFrontend;

class DefaultFrontendPlugin
{
    /**
     * Remove other options than "In Stock", for attribute code "madhat_inventory_status"
     *
     * @param DefaultFrontend $subject
     * @param array $options
     * @return array
     */
    public function afterGetSelectOptions(
        DefaultFrontend $subject,
        array $options
    ) {
        if ($subject->getAttribute()->getAttributeCode() == "madhat_inventory_status") {
            $newOptions = [];
            foreach ($options as $option) {
                if ($option['value'] == 1) {
                    $newOptions[] = $option;
                }
            }
            $options = $newOptions;
        }
        return $options;
    }
}
