<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Helper;

use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Sales\Model\Order;
use Magento\Store\Model\ScopeInterface;

class Data extends SiteCoreHelper
{
    /**
     * Config path for SiteIntegration Order 'Is Enabled' config
     */
    protected const SITE_ORDER_IS_ENABLED_PATH = "site_order/general/is_enabled";

    /**
     * Config path for SiteIntegration Order 'Customer Type' config
     */
    protected const SITE_ORDER_CUSTOMER_TYPE_PATH = "site_order/general/customer_type";

    /**
     * Config path for SiteIntegration Order 'Portal Value' config
     */
    protected const SITE_ORDER_PORTAL_VALUE_PATH = "site_order/general/portal_value";

    /**
     * Config path for SiteIntegration Order 'Shipping Methods Mapping' config
     */
    protected const SITE_ORDER_SHIPPING_METHODS_PATH = "site_order/general/shipping_methods_mapping";

    /**
     * Config path for SiteIntegration Order 'Payment Methods Mapping' config
     */
    protected const SITE_ORDER_PAYMENT_METHODS_PATH = "site_order/general/payment_methods_mapping";

    /**
     * Config path for SiteIntegration Order 'Payment Methods Capture Mapping' config
     */
    protected const SITE_ORDER_PAYMENT_METHODS_CAPTURE_PATH = "site_order/general/payment_methods_capture_mapping";

    /**
     * Config path for SiteIntegration Order 'SITE Order Max Failed Export Attempt' config
     */
    protected const SITE_ORDER_EXPORT_MAX_ATTEMPTS_PATH = "site_order/general/order_export_attempt_count";

    /**
     * Config path for SiteIntegration Order 'Custom SITE Products' config
     */
    protected const SITE_ORDER_CUSTOM_SITE_PRODUCTS_PATH = "site_order/general/custom_site_products";

    /**
     * Config path for SiteIntegration Order 'Vat Mapping' config
     */
    protected const SITE_ORDER_VAT_MAPPING_PATH = "site_order/general/order_vat_mapping";

    /**
     * Config path for SiteIntegration Order 'Order Record Notification Recipients' config
     */
    protected const SITE_ORDER_EMAIL_RECIPIENTS_PATH = "site_order/email/recipient_emails";

    /**
     * Config path for SiteIntegration Order 'Failed Order Record Notification Template' config
     */
    protected const SITE_ORDER_FAIL_NOTIFICATION_TEMPLATE_PATH = "site_order/email/export_report_template";

    protected const GENERAL_LOCALE_CODE_PATH = 'general/locale/code';

    protected const GENERAL_EU_COUNTRIES_PATH = 'general/country/eu_countries';

    /**
     * Config path for SiteIntegration Order 'Allowed Offline Payments' config
     */
    protected const SITE_ORDER_ALLOWED_OFFLINE_PAYMENTS_PATH = "site_order/general/allowed_offline_payments";

    /**
     * @var Json
     */
    private Json $serializer;

    /**
     * @param Context $context
     * @param EncryptorInterface $encryptor
     * @param Json $serializer
     */
    public function __construct(
        Context $context,
        EncryptorInterface $encryptor,
        Json $serializer,
    )
    {
        parent::__construct($context, $encryptor);
        $this->serializer = $serializer;
    }

    /**
     * Returns value for 'Is Enabled' config
     *
     * @return string
     */
    public function getIsEnabled(): string
    {
        return $this->scopeConfig->getValue(
            self::SITE_ORDER_IS_ENABLED_PATH,
            ScopeInterface::SCOPE_WEBSITE
        );
    }

    /**
     * Returns value for 'Customer Type' config
     *
     * @param Order $order
     * @return string
     */
    public function getCustomerType(Order $order): string
    {
        return $this->scopeConfig->getValue(
            self::SITE_ORDER_CUSTOMER_TYPE_PATH,
            ScopeInterface::SCOPE_STORE,
            $order->getStoreId()
        );
    }

    /**
     * Returns value for 'Portal Value' config
     *
     * @param string $websiteId
     * @return string
     */
    public function getPortalValue(string $websiteId = ''): string
    {
        return $this->scopeConfig->getValue(
            self::SITE_ORDER_PORTAL_VALUE_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );
    }

    public function getLanguageCode(Order $order)
    {
        return $this->scopeConfig->getValue(
            self::GENERAL_LOCALE_CODE_PATH,
            ScopeInterface::SCOPE_STORE,
            $order->getStoreId()
        );
    }

    /**
     * Returns value for 'Shipping Methods Mapping' config
     *
     * @param string $websiteId
     * @return string
     */
    public function getShippingMethodsMapping(string $websiteId = ''): string
    {
        return $this->scopeConfig->getValue(
            self::SITE_ORDER_SHIPPING_METHODS_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );
    }

    /**
     * Returns value for 'Payment Methods Mapping' config
     *
     * @param string $websiteId
     * @return string
     */
    public function getPaymentMethodsMapping(string $websiteId = ''): string
    {
        $websiteId = '' ? 0 : (int) $websiteId;
        return $this->scopeConfig->getValue(
            self::SITE_ORDER_PAYMENT_METHODS_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );
    }

    public function getPaymentMethodsCaptureMapping(string $websiteId = ''): string
    {
        $websiteId = '' ? 0 : (int) $websiteId;
        return $this->scopeConfig->getValue(
            self::SITE_ORDER_PAYMENT_METHODS_CAPTURE_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );
    }

    /**
     * Get Max Attempt Count for SITE Order Export
     *
     * @param string $websiteId
     * @return int
     */
    public function getExportMaxAttempts(string $websiteId = ''): int
    {
        return (int) $this->scopeConfig->getValue(
            self::SITE_ORDER_EXPORT_MAX_ATTEMPTS_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );
    }

    /**
     * Get Email Recipients for SITE Order Export
     *
     * @return array
     */
    public function getSiteOrderRecipients(): array
    {
        $emailIds = $this->scopeConfig->getValue(
            self::SITE_ORDER_EMAIL_RECIPIENTS_PATH
        );

        return $emailIds ? explode(',', $emailIds) : [];
    }

    /**
     * Get Email Template for SITE Order Export Fail Notification
     *
     * @return string
     */
    public function getSiteOrderFailTemplate(): string
    {
        return $this->scopeConfig->getValue(
            self::SITE_ORDER_FAIL_NOTIFICATION_TEMPLATE_PATH
        );
    }

    /**
     * Get Custom SITE Products from config
     *
     * @param string $websiteId
     * @return array
     */
    public function getCustomSiteProducts(string $websiteId = ''): array
    {
        $customSiteProducts = $this->scopeConfig->getValue(
            self::SITE_ORDER_CUSTOM_SITE_PRODUCTS_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );

        return $customSiteProducts ? explode(',', $customSiteProducts) : [];
    }

    /**
     * Get Custom SITE Products from config
     *
     * @param string $websiteId
     * @return array
     */
    public function getOrderVatMapping(string $websiteId = ''): array
    {
        $vatMapping = $this->scopeConfig->getValue(
            self::SITE_ORDER_VAT_MAPPING_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );

        if (!$vatMapping) {
            return [];
        }

        return $this->serializer->unserialize($vatMapping);
    }

    /**
     * Return array of allowed offline payment methods.
     *
     * @param string $websiteId
     * @return array
     */
    public function getAllowedOfflinePaymentMethodsFromConfig(string $websiteId): array
    {
        $allowedOfflinePayments = $this->scopeConfig->getValue(
            self::SITE_ORDER_ALLOWED_OFFLINE_PAYMENTS_PATH,
            ScopeInterface::SCOPE_WEBSITE,
            $websiteId
        );

        return $allowedOfflinePayments ? explode(',', $allowedOfflinePayments) : [];
    }

    /**
     * Return array of Northern Countries Code
     *
     * @return string[]
     */
    public function getNorthernCountries(): array
    {
        return ['SE','NO','FI','DK'];
    }

    /**
     * Returns value for General 'EU Countries' config
     *
     * @return false|array
     */
    public function getEuropeanCountries(): false|array
    {
        return explode(
            ',',
            $this->scopeConfig->getValue(
                self::GENERAL_EU_COUNTRIES_PATH,
                ScopeInterface::SCOPE_STORE
            )
        );
    }

    public function getSiteExVatMap(): array
    {
        return [
            'B2B' => [
                'SE' => [
                    'validvat' => 0,
                    'invalidvat' => 0,
                ],
                'NO' => [
                    'validvat' => 2,
                    'invalidvat' => 0,
                ],
                'DK' => [
                    'validvat' => 2,
                    'invalidvat' => 0,
                ],
                'FI' => [
                    'validvat' => 2,
                    'invalidvat' => 0,
                ],
                'EUROPE' => [
                    'validvat' => 2,
                    'invalidvat' => 0,
                ],
                'OTHER' => [
                    'validvat' => 3,
                    'invalidvat' => 3,
                ]
            ],
            'B2C' => [
                'SE' => [
                    'validvat' => 0,
                    'invalidvat' => 0,
                ],
                'NO' => [
                    'validvat' => 0,
                    'invalidvat' => 0,
                ],
                'DK' => [
                    'validvat' => 0,
                    'invalidvat' => 0,
                ],
                'FI' => [
                    'validvat' => 0,
                    'invalidvat' => 0,
                ],
                'EUROPE' => [
                    'validvat' => 0,
                    'invalidvat' => 0,
                ],
                'OTHER' => [
                    'validvat' => 3,
                    'invalidvat' => 3,
                ]
            ]
        ];
    }

    public function getSiteCustomerVatAccountMap($countryId)
    {
        return [
            'B2B' => [
                'SE' => [
                    'validvat' => 752,
                    'invalidvat' => 752,
                ],
                'NO' => [
                    'validvat' => 23,
                    'invalidvat' => 578,
                ],
                'DK' => [
                    'validvat' => 23,
                    'invalidvat' => 208,
                ],
                'FI' => [
                    'validvat' => 23,
                    'invalidvat' => 246,
                ],
                'EUROPE' => [
                    'validvat' => 23,
                    'invalidvat' => $this->getIso3166Numeric($countryId),
                ],
                'OTHER' => [
                    'validvat' => 21,
                    'invalidvat' => 21,
                ]
            ],
            'B2C' => [
                'SE' => [
                    'validvat' => 752,
                    'invalidvat' => 752,
                ],
                'NO' => [
                    'validvat' => 578,
                    'invalidvat' => 578,
                ],
                'DK' => [
                    'validvat' => 208,
                    'invalidvat' => 208,
                ],
                'FI' => [
                    'validvat' => 246,
                    'invalidvat' => 246,
                ],
                'EUROPE' => [
                    'validvat' => $this->getIso3166Numeric($countryId),
                    'invalidvat' => $this->getIso3166Numeric($countryId),
                ],
                'OTHER' => [
                    'validvat' => 21,
                    'invalidvat' => 21,
                ]
            ]
        ];
    }

    /**
     * @param $countryId
     * @return mixed
     */
    public function getIso3166Numeric($countryId): mixed
    {
        $map = [
            'AF' => 4,
            'AL' => 8,
            'DZ' => 12,
            'AS' => 16,
            'AD' => 20,
            'AO' => 24,
            'AI' => 660,
            'AQ' => 10,
            'AG' => 28,
            'AR' => 32,
            'AM' => 51,
            'AW' => 533,
            'AU' => 36,
            'AT' => 40,
            'AZ' => 31,
            'BS' => 44,
            'BH' => 48,
            'BD' => 50,
            'BB' => 52,
            'BY' => 112,
            'BE' => 56,
            'BZ' => 84,
            'BJ' => 204,
            'BM' => 60,
            'BT' => 64,
            'BO' => 68,
            'BQ' => 535,
            'BA' => 70,
            'BW' => 72,
            'BV' => 74,
            'BR' => 76,
            'IO' => 86,
            'BN' => 96,
            'BG' => 100,
            'BF' => 854,
            'BI' => 108,
            'CV' => 132,
            'KH' => 116,
            'CM' => 120,
            'CA' => 124,
            'KY' => 136,
            'CF' => 140,
            'TD' => 148,
            'CL' => 152,
            'CN' => 156,
            'CX' => 162,
            'CC' => 166,
            'CO' => 170,
            'KM' => 174,
            'CD' => 180,
            'CG' => 178,
            'CK' => 184,
            'CR' => 188,
            'HR' => 191,
            'CU' => 192,
            'CW' => 531,
            'CY' => 196,
            'CZ' => 203,
            'CI' => 384,
            'DK' => 208,
            'DJ' => 262,
            'DM' => 212,
            'DO' => 214,
            'EC' => 218,
            'EG' => 818,
            'SV' => 222,
            'GQ' => 226,
            'ER' => 232,
            'EE' => 233,
            'SZ' => 748,
            'ET' => 231,
            'FK' => 238,
            'FO' => 234,
            'FJ' => 242,
            'FI' => 246,
            'FR' => 250,
            'GF' => 254,
            'PF' => 258,
            'TF' => 260,
            'GA' => 266,
            'GM' => 270,
            'GE' => 268,
            'DE' => 276,
            'GH' => 288,
            'GI' => 292,
            'GR' => 300,
            'GL' => 304,
            'GD' => 308,
            'GP' => 312,
            'GU' => 316,
            'GT' => 320,
            'GG' => 831,
            'GN' => 324,
            'GW' => 624,
            'GY' => 328,
            'HT' => 332,
            'HM' => 334,
            'VA' => 336,
            'HN' => 340,
            'HK' => 344,
            'HU' => 348,
            'IS' => 352,
            'IN' => 356,
            'ID' => 360,
            'IR' => 364,
            'IQ' => 368,
            'IE' => 372,
            'IM' => 833,
            'IL' => 376,
            'IT' => 380,
            'JM' => 388,
            'JP' => 392,
            'JE' => 832,
            'JO' => 400,
            'KZ' => 398,
            'KE' => 404,
            'KI' => 296,
            'KP' => 408,
            'KR' => 410,
            'KW' => 414,
            'KG' => 417,
            'LA' => 418,
            'LV' => 428,
            'LB' => 422,
            'LS' => 426,
            'LR' => 430,
            'LY' => 434,
            'LI' => 438,
            'LT' => 440,
            'LU' => 442,
            'MO' => 446,
            'MG' => 450,
            'MW' => 454,
            'MY' => 458,
            'MV' => 462,
            'ML' => 466,
            'MT' => 470,
            'MH' => 584,
            'MQ' => 474,
            'MR' => 478,
            'MU' => 480,
            'YT' => 175,
            'MX' => 484,
            'FM' => 583,
            'MD' => 498,
            'MC' => 492,
            'MN' => 496,
            'ME' => 499,
            'MS' => 500,
            'MA' => 504,
            'MZ' => 508,
            'MM' => 104,
            'NA' => 516,
            'NR' => 520,
            'NP' => 524,
            'NL' => 528,
            'NC' => 540,
            'NZ' => 554,
            'NI' => 558,
            'NE' => 562,
            'NG' => 566,
            'NU' => 570,
            'NF' => 574,
            'MK' => 807,
            'MP' => 580,
            'NO' => 578,
            'OM' => 512,
            'PK' => 586,
            'PW' => 585,
            'PS' => 275,
            'PA' => 591,
            'PG' => 598,
            'PY' => 600,
            'PE' => 604,
            'PH' => 608,
            'PN' => 612,
            'PL' => 616,
            'PT' => 620,
            'PR' => 630,
            'QA' => 634,
            'RO' => 642,
            'RU' => 643,
            'RW' => 646,
            'RE' => 638,
            'BL' => 652,
            'SH' => 654,
            'KN' => 659,
            'LC' => 662,
            'MF' => 663,
            'PM' => 666,
            'VC' => 670,
            'WS' => 882,
            'SM' => 674,
            'ST' => 678,
            'SA' => 682,
            'SN' => 686,
            'RS' => 688,
            'SC' => 690,
            'SL' => 694,
            'SG' => 702,
            'SX' => 534,
            'SK' => 703,
            'SI' => 705,
            'SB' => 90,
            'SO' => 706,
            'ZA' => 710,
            'GS' => 239,
            'SS' => 728,
            'ES' => 724,
            'LK' => 144,
            'SD' => 729,
            'SR' => 740,
            'SJ' => 744,
            'SE' => 752,
            'CH' => 756,
            'SY' => 760,
            'TW' => 158,
            'TJ' => 762,
            'TZ' => 834,
            'TH' => 764,
            'TL' => 626,
            'TG' => 768,
            'TK' => 772,
            'TO' => 776,
            'TT' => 780,
            'TN' => 788,
            'TR' => 792,
            'TM' => 795,
            'TC' => 796,
            'TV' => 798,
            'UG' => 800,
            'UA' => 804,
            'AE' => 784,
            'GB' => 826,
            'UM' => 581,
            'US' => 840,
            'UY' => 858,
            'UZ' => 860,
            'VU' => 548,
            'VE' => 862,
            'VN' => 704,
            'VG' => 92,
            'VI' => 850,
            'WF' => 876,
            'EH' => 732,
            'YE' => 887,
            'ZM' => 894,
            'ZW' => 716,
            'AX' => 248
        ];
        return $map[$countryId];
    }
}
