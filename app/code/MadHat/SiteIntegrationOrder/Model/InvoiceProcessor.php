<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Model;

use MadHat\SiteIntegrationCore\Model\Api\SiteApi;
use MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface;
use MadHat\OrderIntegration\Model\ResourceModel\MadhatOrderInfo\CollectionFactory as MadhatOrderInfoCollectionFactory;
use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Logger\Logger;
use MadHat\SiteIntegrationOrder\Model\Api\SiteOrderApi;
use Magento\Framework\DB\Transaction;
use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\ResourceModel\Order\Collection;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Magento\Sales\Model\Service\InvoiceService;

class InvoiceProcessor
{
    /**
     * @var MadhatOrderInfoCollectionFactory
     */
    protected MadhatOrderInfoCollectionFactory $madhatOrderInfoCollectionFactory;

    /**
     * @var SiteOrderApi
     */
    protected SiteOrderApi $siteOrderApi;

    /**
     * @var Transaction
     */
    protected Transaction $transaction;

    /**
     * @var OrderRepositoryInterface
     */
    protected OrderRepositoryInterface $orderRepository;

    /**
     * @var CollectionFactory
     */
    protected CollectionFactory $orderCollectionFactory;

    /**
     * @var InvoiceService
     */
    protected InvoiceService $invoiceService;
    private MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository;

    /**
     * @var Data
     */
    protected Data $siteOrderHelper;

    /**
     * @var Logger
     */
    protected Logger $siteOrderLogger;
    private SiteApi $siteApi;

    /**
     * @param SiteApi $siteApi
     * @param MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository
     * @param MadhatOrderInfoCollectionFactory $madhatOrderInfoCollectionFactory
     * @param SiteOrderApi $siteOrderApi
     * @param Transaction $transaction
     * @param OrderRepositoryInterface $orderRepository
     * @param CollectionFactory $orderCollectionFactory
     * @param InvoiceService $invoiceService
     * @param Data $siteOrderHelper
     * @param Logger $siteOrderLogger
     */
    public function __construct(
        SiteApi $siteApi,
        MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository,
        MadhatOrderInfoCollectionFactory $madhatOrderInfoCollectionFactory,
        SiteOrderApi $siteOrderApi,
        Transaction $transaction,
        OrderRepositoryInterface $orderRepository,
        CollectionFactory $orderCollectionFactory,
        InvoiceService $invoiceService,
        Data $siteOrderHelper,
        Logger $siteOrderLogger
    ) {
        $this->siteApi = $siteApi;
        $this->madhatOrderInfoRepository = $madhatOrderInfoRepository;
        $this->madhatOrderInfoCollectionFactory = $madhatOrderInfoCollectionFactory;
        $this->siteOrderApi = $siteOrderApi;
        $this->transaction = $transaction;
        $this->orderRepository = $orderRepository;
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->invoiceService = $invoiceService;
        $this->siteOrderHelper = $siteOrderHelper;
        $this->siteOrderLogger = $siteOrderLogger;
    }

    /**
     * Capture Order Payments.
     *
     * @param array $incrementIds
     * @return array
     */
    public function capturePayments(array $incrementIds = []): array
    {
        $this->siteOrderLogger->info('SiteIntegrationOrder::capturePayments starts');
        $orderCollection = $this->filterOrderCollectionForCapturePayment($incrementIds);

        $result['success'] = false;
        $result['total_orders'] = $orderCollection->getSize();
        $result['success_orders'] = 0;
        $result['failed_orders'] = 0;
        $result['error_messages'] = [];

        if ($orderCollection->getSize()) {
            $magentoOrderIds = $orderCollection->getColumnValues('entity_id');
            $this->siteOrderLogger->info('SiteIntegrationOrder::capturePayments for order ids : ' . implode(',', $magentoOrderIds));
            foreach ($orderCollection as $order) {
                $magentoOrder = $this->orderRepository->get($order->getEntityId());
                $orderPaymentStatus = $this->generateOrderInvoice($magentoOrder);

                if ($orderPaymentStatus['success']) {
                    $result['success_orders']++;
                } else {
                    $result['failed_orders']++;
                    $result['error_messages'][] = $orderPaymentStatus['error'];
                }
            }
            $result['message'] = __(
                '%1 out of %2 order\'s amount captured.',
                $result['success_orders'],
                $result['total_orders']
            );
        } else {
            $this->siteOrderLogger->info('SiteIntegrationOrder::capturePayments => no order found');
            $result['message'] = __(
                'No orders were found to capture payment.'
            );
        }

        if ($result['success_orders'] > 0) {
            $result['success'] = true;
        }

        if ($result['total_orders'] > 0) {
            $this->siteOrderApi->logReportToDbLogger($result, 'Magento Order Payment Capture Report');
        }
        $this->siteOrderLogger->info('SiteIntegrationOrder::capturePayments ends');

        return $result;
    }

    /**
     * Filter Order Collection for Capture Payment
     *
     * @param array $incrementIds
     * @return Collection
     */
    protected function filterOrderCollectionForCapturePayment(array $incrementIds = []): Collection
    {
        $orderCollection = $this->orderCollectionFactory->create();

        $orderCollection->addFieldToSelect(['increment_id', 'status'])
            ->getSelect()
            ->join(
                ['sop' => $orderCollection->getTable('sales_order_payment')],
                'main_table.entity_id = sop.parent_id',
                ['cc_trans_id', 'method']
            )
            ->joinLeft(
                ['an' => $orderCollection->getTable('adyen_notification')],
                'an.pspreference = sop.cc_trans_id AND an.merchant_reference = main_table.increment_id',
                ['event_code', 'success', 'done']
            )
            ->joinLeft(
                ['moi' => $orderCollection->getTable('madhat_order_info')],
                'moi.order_id = main_table.entity_id',
                ['site_order_id','site_is_picked','site_is_captured','site_is_invoiced']
            );

        $orderCollection
            ->addFieldToFilter('status', ['nin' => ['canceled', 'closed','complete','holded','payment_review']])
            ->addFieldToFilter('moi.site_order_id', ['notnull' => true])
            ->addFieldToFilter('moi.site_is_picked', 1)
            ->addFieldToFilter('moi.site_is_captured', ['null' => true])
            ->addFieldToFilter('moi.site_is_invoiced',  ['null' => true]);

        // Add conditional logic for Adyen checks, not Offline Payment Methods
        $allowedOfflinePaymentMethods = $this->siteOrderHelper->getAllowedOfflinePaymentMethodsFromConfig('1');
        $connection = $orderCollection->getConnection();
        $select = $orderCollection->getSelect();

        $select->where(
            '(' .
            $connection->quoteInto('sop.method NOT IN (?)', $allowedOfflinePaymentMethods) .
            ' AND (' .
            'sop.cc_trans_id IS NOT NULL AND ' .
            $connection->quoteInto('an.event_code = ?', 'AUTHORISATION') . ' AND ' .
            $connection->quoteInto('an.success = ?', 'true') . ' AND ' .
            $connection->quoteInto('an.done = ?', '1') .
            ')' .
            ')'
        );

        if (!empty($incrementIds)) {
            $orderCollection->addFieldToFilter('increment_id', $incrementIds);
        }

        return $orderCollection;
    }

    /**
     * Generate invoice for Order
     *
     * @param Order $order
     * @return array
     */
    public function generateOrderInvoice(Order $order): array
    {
        $result = [];
        $acceptedPaymentMethodsConfigValue = $this->siteOrderHelper->getPaymentMethodsMapping();
        $acceptedPaymentMethods = array_keys(json_decode($acceptedPaymentMethodsConfigValue, true));
        try {
            if ($order->canInvoice() && in_array($order->getPayment()->getMethod(), $acceptedPaymentMethods)) {
                $invoice = $this->invoiceService->prepareInvoice($order);
                if (!$invoice->getTotalQty()) {
                    throw new LocalizedException(
                        __("The invoice can't be created without products. Add products and try again.")
                    );
                }
                // Visma is a master system so we should set total for invoice from Visma
                $siteOrderParams = ['WebOrderNo' => $order->getEntityId()];
                $response = $this->siteApi->orderstatus($siteOrderParams);
                if (isset($response['ResponseResult']) && $response['ResponseResult']['Code'] == 'SUCCESS') {
                    $baseCurrency = $order->getBaseCurrencyCode();
                    // Check if we need currency conversion
                    $responseCurrency = $response['OrderStatusData']['CurrencyISO'];
                    $needsConversion = ($baseCurrency !== $responseCurrency);
                    $amountGross = $response['OrderStatusData']['AmountGross'];
                    $invoice->setGrandTotal($amountGross);
                    $invoice->setBaseGrandTotal($amountGross);
                    if ($needsConversion) {
                        $exchangeRate = $this->siteOrderApi->getCurrencyExchangeRate($responseCurrency, $baseCurrency);
                        $baseGrandTotal = $amountGross * $exchangeRate;
                        $invoice->setBaseCurrencyCode($baseCurrency);
                        $invoice->setOrderCurrencyCode($responseCurrency);
                        $invoice->setBaseGrandTotal($baseGrandTotal);
                    }
                } else {
                    // Log to order history
                    $order->addStatusHistoryComment(__('Not SUCCESS response from Visma on generateOrderInvoice: %1', json_encode($response)))
                        ->setIsCustomerNotified(false);
                }


                $invoice->setRequestedCaptureCase('online');
                $invoice->register();
                $invoice->getOrder()->setIsInProcess(true);

                $transactionSave = $this->transaction
                    ->addObject($invoice)
                    ->addObject($invoice->getOrder());
                $transactionSave->save();

                $order->addStatusHistoryComment('Invoice generated using MadHat Invoice Processor.')
                    ->setIsCustomerNotified(true);
                $this->orderRepository->save($order);
                $result['success'] = true;
            } elseif (!$order->hasInvoices()) {
                $result['success'] = false;
                $result['error'] = [
                    'Order Id' => $order->getId(),
                    'Error Message' => __('Order can\'t be invoiced')
                ];
            }
        } catch (\Exception $e) {
            $result['success'] = false;
            $result ['error'] = [
                'Order Id' => $order->getId(),
                'Error Message' => $e->getMessage()
            ];
        }
        return $result;
    }
}
