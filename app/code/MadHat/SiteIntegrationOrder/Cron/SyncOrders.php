<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Cron;

use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Model\Api\SiteOrderApi;
use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;

class SyncOrders
{
    /**
     * @var Data
     */
    private Data $siteOrderHelper;

    /**
     * @var SiteOrderApi
     */
    private SiteOrderApi $siteOrderApi;

    /**
     * @var SiteCoreHelper
     */
    private SiteCoreHelper $siteCoreHelper;

    /**
     * @param Data $siteOrderHelper
     * @param SiteOrderApi $siteOrderApi
     * @param SiteCoreHelper $siteCoreHelper
     */
    public function __construct(
        Data         $siteOrderHelper,
        SiteOrderApi $siteOrderApi,
        SiteCoreHelper $siteCoreHelper
    )
    {
        $this->siteOrderHelper = $siteOrderHelper;
        $this->siteOrderApi = $siteOrderApi;
        $this->siteCoreHelper = $siteCoreHelper;
    }

    /**
     * Export Magento orders to SITE
     *
     * @return void
     */
    public function execute(): void
    {
        if ($this->siteOrderHelper->getIsEnabled() && $this->siteCoreHelper->isCronMode()) {
            $this->siteOrderApi->syncSiteOrders();
        }
    }
}
