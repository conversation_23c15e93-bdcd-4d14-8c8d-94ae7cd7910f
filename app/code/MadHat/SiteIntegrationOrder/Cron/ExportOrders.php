<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Cron;

use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Model\Api\SiteOrderApi;
use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;

class ExportOrders
{
    /**
     * @var Data
     */
    private Data $madhatSiteOrderHelper;

    /**
     * @var SiteOrderApi
     */
    private SiteOrderApi $siteOrderApi;

    /**
     * @var SiteCoreHelper
     */
    private SiteCoreHelper $siteCoreHelper;

    /**
     * @param Data $madhatSiteOrderHelper
     * @param SiteOrderApi $siteOrderApi
     * @param SiteCoreHelper $siteCoreHelper
     */
    public function __construct(
        Data                          $madhatSiteOrderHelper,
        SiteOrderApi                  $siteOrderApi,
        SiteCoreHelper                $siteCoreHelper
    ) {
        $this->madhatSiteOrderHelper = $madhatSiteOrderHelper;
        $this->siteOrderApi = $siteOrderApi;
        $this->siteCoreHelper = $siteCoreHelper;
    }

    /**
     * Export Magento orders to SITE
     *
     * @return void
     */
    public function execute(): void
    {
        if ($this->madhatSiteOrderHelper->getIsEnabled() && $this->siteCoreHelper->isCronMode()) {
            $this->siteOrderApi->exportSiteOrders();
        }
    }
}
