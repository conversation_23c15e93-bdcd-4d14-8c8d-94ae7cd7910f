<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Cron;

use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Model\InvoiceProcessor;
use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;

class CapturePayment
{
    /**
     * @var Data
     */
    private Data $siteOrderHelper;

    /**
     * @var InvoiceProcessor
     */
    private InvoiceProcessor $invoiceProcessor;

    /**
     * @var SiteCoreHelper
     */
    private SiteCoreHelper $siteCoreHelper;

    /**
     * @param Data $siteOrderHelper
     * @param InvoiceProcessor $invoiceProcessor
     * @param SiteCoreHelper $siteCoreHelper
     */
    public function __construct(
        Data         $siteOrderHelper,
        InvoiceProcessor $invoiceProcessor,
        SiteCoreHelper $siteCoreHelper
    )
    {
        $this->siteOrderHelper = $siteOrderHelper;
        $this->invoiceProcessor = $invoiceProcessor;
        $this->siteCoreHelper = $siteCoreHelper;
    }

    /**
     * Export Magento orders to SITE
     *
     * @return void
     */
    public function execute(): void
    {
        if ($this->siteOrderHelper->getIsEnabled() && $this->siteCoreHelper->isCronMode()) {
            $this->invoiceProcessor->capturePayments();
        }
    }
}
