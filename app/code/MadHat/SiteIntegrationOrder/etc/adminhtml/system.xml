<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="site_order" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1" translate="label">
            <label>SITE Integration Order</label>
            <tab>madhat</tab>
            <resource>MadHat_SiteIntegrationOrder::madhat_site_order_config</resource>
            <group id="general" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" translate="label">
                <label>General</label>
                <field id="is_enabled" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>Enable SITE Order Integration</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="customer_type" type="select" sortOrder="10" showInDefault="1" showInWebsite="1"
                       showInStore="1"
                       translate="label" canRestore="1">
                    <label>Customers type</label>
                    <source_model>MadHat\SiteIntegrationOrder\Model\Config\Source\CustomerType</source_model>
                </field>
                <field id="portal_value" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>Portal Value</label>
                    <source_model>MadHat\SiteIntegrationOrder\Model\Config\Source\PortalValue</source_model>
                </field>
                <field id="shipping_methods_mapping" type="textarea" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0" translate="label comment">
                    <label>Shipping Methods Mapping</label>
                    <comment>Store JSON string for Shipping Methods Mapping</comment>
                </field>
                <field id="payment_methods_mapping" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0" canRestore="1" translate="label comment">
                    <label>Payment Methods Mapping</label>
                    <comment>Store JSON string for Payment Methods Mapping</comment>
                </field>
                <field id="payment_methods_capture_mapping" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="0" showInStore="0" canRestore="1" translate="label comment">
                    <label>Payment Methods Capture Mapping</label>
                    <comment>Store JSON string for Payment Methods Capture Mapping</comment>
                </field>
                <field id="order_vat_mapping" type="textarea" sortOrder="45" showInDefault="1" showInWebsite="1" showInStore="0" canRestore="1" translate="label comment">
                    <label>CustomerVatAccount Mapping</label>
                    <comment>Store JSON string for CustomerVatAccount Mapping w.r.t. Country Code</comment>
                </field>
                <field id="order_export_attempt_count" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="0" translate="label comment">
                    <label>Order Export Attempt Count</label>
                    <validate>validate-range range-1-5</validate>
                    <comment>Please enter the value between 1 and 5.</comment>
                </field>
                <field id="custom_site_products" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="0" translate="label comment">
                    <label>Custom SITE Products</label>
                    <comment>Comma seperated custom SITE products to ignore during Order Edit Process</comment>
                </field>
                <field id="allowed_offline_payments" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="0" translate="label comment">
                    <label>Allowed Offline(Manual) Payment Methods</label>
                    <comment>Comma seperated offline(manual) payment methods to include in order export process</comment>
                </field>
            </group>
            <group id="email" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0" translate="label">
                <label>Email Settings</label>
                <field id="recipient_emails" type="textarea" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0" translate="label comment" >
                    <label>Recipient Email Ids</label>
                    <comment>Comma seperated Email Ids.</comment>
                    <validate>required-entry</validate>
                </field>
                <field id="export_report_template" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0" translate="label">
                    <label>Email Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <validate>required-entry</validate>
                </field>
            </group>
        </section>
    </system>
</config>
